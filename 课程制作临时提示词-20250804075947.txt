# 金融入门9节课程制作专用临时提示词说明书

## 课程基本信息
- 课题：金融入门
- 节数：9节课程
- 目标学员：零基础学员
- 课程类型：知识普及类
- 制作时间：2025-08-04 07:59:47

## 课程大纲设计

### 基础入门（第1-3节）
1. 第1节：什么是金融？金融的基本概念和作用
2. 第2节：货币与银行：金融体系的基础
3. 第3节：利率与通胀：影响我们生活的金融指标

### 核心内容（第4-6节）
4. 第4节：投资基础：股票、债券、基金入门
5. 第5节：风险与收益：投资中的基本原理
6. 第6节：个人理财：如何管理自己的财务

### 进阶应用（第7-9节）
7. 第7节：金融市场：股市、债市、外汇市场简介
8. 第8节：金融科技：数字支付、网络银行、区块链
9. 第9节：金融规划：制定个人财务目标和计划

## 每节课内容要求
- 字数：每节课正文不少于300字
- 结构：学习目标、核心内容、实例说明、重点总结、课后思考、下节预告
- 语言：通俗易懂，适合零基础学员
- 案例：每节课至少2个具体案例

## 技术实现要求
- 文件名：20250804075947-jinrongrumen9jiekecheng.php
- 界面风格：AI风格深色科技主题
- 移动端优化：手机端优先设计
- 语音功能：集成Edge-TTS语音朗读
- Loading动画：集成用户提供的苹果风格Loading效果
- 底部导航：固定底部导航栏，包含上下节和播放功能

## 特殊功能集成
1. 苹果风格Loading动画（用户提供的详细设计）
2. 语音朗读功能（Edge-TTS API）
3. 音频缓存机制
4. 移动端伸缩导航
5. 玻璃透明效果
6. 粒子背景动画

## 服务器配置
- FTP服务器：8.138.203.25:21
- 用户名：diwangzhidao
- 密码：88888888
- 上传路径：/MCP/xiangliang/1/
- 访问地址：https://www.diwangzhidao.com/MCP/xiangliang/1/

## 邮件配置
- SMTP：smtp.qq.com:465
- 发件人：项老师超级总裁助理001 <<EMAIL>>
- 收件人：<EMAIL>
- 主题：金融入门9节课程已完成 - 用时${duration}

## 质量标准
- 内容完整性：严禁使用占位符
- 逻辑清晰：循序渐进，前后呼应
- 实用性强：理论结合实践
- 移动端体验：触控优化，响应式设计
- 加载性能：页面加载时间不超过3秒

## 执行检查清单
1. ✓ 任务开始计时
2. ✓ 生成临时提示词
3. [ ] 提取课题关键词
4. [ ] 网络搜索课题资料
5. [ ] 制定课程大纲
6. [ ] 课程结构设计
7. [ ] 创建PHP基础框架
8. [ ] 添加AI风格样式
9. [ ] 完成页面框架
10. [ ] 生成9节课程内容
11. [ ] 最终优化检查
12. [ ] 计算任务用时
13. [ ] 上传PHP文件
14. [ ] 发送完成邮件

## 注意事项
- 严格按照协议执行，不得跳过任何步骤
- 所有操作必须真实执行，严禁模拟
- 确保内容质量，每节课都要详细完整
- 移动端体验优先，确保在手机上使用流畅
- 集成用户提供的Loading动画设计要求
