Add-Type -AssemblyName System.Net.Mail

$smtpServer = "smtp.qq.com"
$smtpPort = 465
$username = "<EMAIL>"
$password = "nsczwndkbuljbihj"
$from = "<EMAIL>"
$to = "<EMAIL>"
$subject = "Finance Course Completed - 13min36sec"

$body = "Dear Mr. Xiang,

The finance course task has been completed successfully:

Task: Finance Introduction 9-Lesson Course
Duration: 13 minutes 36 seconds
Time: 2025-08-04 07:59:47 to 2025-08-04 08:13:23
File: 20250804075947-jinrongrumen9jiekecheng.php

Course includes:
- 9 complete lessons with 300+ words each
- AI-style interface design
- Loading animation
- Voice reading function
- Mobile optimization

Best regards,
AI Assistant 001
2025-08-04 08:13:23"

try {
    $smtp = New-Object System.Net.Mail.SmtpClient($smtpServer, $smtpPort)
    $smtp.EnableSsl = $true
    $smtp.Credentials = New-Object System.Net.NetworkCredential($username, $password)
    
    $message = New-Object System.Net.Mail.MailMessage($from, $to, $subject, $body)
    $message.IsBodyHtml = $false
    
    $smtp.Send($message)
    Write-Host "Email sent successfully!"
    Write-Host "To: $to"
    Write-Host "Subject: $subject"
} catch {
    Write-Host "Email failed: $($_.Exception.Message)"
}
