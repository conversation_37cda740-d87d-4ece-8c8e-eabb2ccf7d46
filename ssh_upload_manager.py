#!/usr/bin/env python3
import paramiko
import os
import sys

def ssh_upload_file():
    # SSH连接配置
    host = '************'
    username = 'root'
    password = 'Zxczxczxc111.'
    
    # 文件路径配置
    local_file = '20250804083850-jinglirenzezesuyangbiaozhun9jiekecheng.php'
    remote_file = '/www/wwwroot/www.diwangzhidao.com/MCP/xiangliang/1/20250804083850-jinglirenzezesuyangbiaozhun9jiekecheng.php'
    
    try:
        print("正在建立SSH连接...")
        # 1. 建立SSH连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password, timeout=30)
        
        print("SSH连接成功，创建SFTP会话...")
        # 2. 创建SFTP会话
        sftp = ssh.open_sftp()
        
        print("正在上传文件...")
        # 3. 上传文件
        sftp.put(local_file, remote_file)
        
        print("设置文件权限...")
        # 4. 设置权限
        sftp.chmod(remote_file, 0o644)
        
        # 获取文件大小确认上传成功
        file_stat = sftp.stat(remote_file)
        local_size = os.path.getsize(local_file)
        
        print(f"上传成功！")
        print(f"本地文件大小: {local_size} 字节")
        print(f"远程文件大小: {file_stat.st_size} 字节")
        print(f"访问地址: https://www.diwangzhidao.com/MCP/xiangliang/1/20250804083850-jinglirenzezesuyangbiaozhun9jiekecheng.php")
        
        # 5. 关闭连接
        sftp.close()
        ssh.close()
        
        return True
        
    except Exception as e:
        print(f"上传失败: {str(e)}")
        return False

if __name__ == "__main__":
    ssh_upload_file()
