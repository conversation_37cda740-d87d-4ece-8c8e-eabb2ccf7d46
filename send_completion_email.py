#!/usr/bin/env python3
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header

def send_completion_email():
    # 邮件配置
    smtp_server = "smtp.qq.com"
    smtp_port = 465
    username = "<EMAIL>"
    password = "nsczwndkbuljbihj"
    from_email = "<EMAIL>"
    to_email = "<EMAIL>"
    
    # 邮件内容
    subject = "经理人职责素养标准9节课程已完成 - 用时11分37秒"
    
    html_body = """
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>经理人职责素养标准9节课程已完成</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center; color: white;">
      <h1>经理人职责素养标准9节课程</h1>
      <p>完成通知</p>
    </div>
    <div style="padding: 20px; background: #f8f9fa;">
      <h3>尊敬的项老师，您好！</h3>
      <p>您指示的任务已经完成：</p>
      <ul>
        <li><strong>任务名称：</strong>经理人职责素养标准9节课程</li>
        <li><strong>完成时间：</strong>2025-08-04 08:38:50～2025-08-04 08:50:27</li>
        <li><strong>用时：</strong>11分37秒</li>
        <li><strong>课程内容：</strong>9节完整课程，每节300+字详细内容</li>
        <li><strong>特色功能：</strong>AI风格界面、语音朗读、移动端优化</li>
      </ul>
      <div style="text-align: center; margin: 20px 0;">
        <a href="https://www.diwangzhidao.com/MCP/xiangliang/1/20250804083850-jinglirenzezesuyangbiaozhun9jiekecheng.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔗 查看课程</a>
      </div>
      <h4>课程大纲：</h4>
      <ol>
        <li>经理人的角色定位与核心职责</li>
        <li>现代经理人必备的基本素养</li>
        <li>经理人的行为标准与职业操守</li>
        <li>团队管理与人员激励技巧</li>
        <li>沟通协调与冲突解决能力</li>
        <li>决策制定与执行力提升</li>
        <li>绩效管理与目标达成方法</li>
        <li>变革领导与创新思维培养</li>
        <li>经理人的自我提升与职业发展</li>
      </ol>
      <p>此致<br>敬礼！</p>
      <p><strong>项老师超级总裁助理001</strong><br>2025-08-04 08:50:27</p>
    </div>
  </div>
</body>
</html>
    """
    
    try:
        # 创建邮件对象
        msg = MIMEMultipart('alternative')
        msg['From'] = Header("项老师超级总裁助理001", 'utf-8').encode() + f" <{from_email}>"
        msg['To'] = to_email
        msg['Subject'] = Header(subject, 'utf-8')
        
        # 添加HTML内容
        html_part = MIMEText(html_body, 'html', 'utf-8')
        msg.attach(html_part)
        
        # 连接SMTP服务器并发送邮件
        print("正在连接SMTP服务器...")
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(username, password)
        
        print("正在发送邮件...")
        text = msg.as_string()
        server.sendmail(from_email, to_email, text)
        server.quit()
        
        print("邮件发送成功！")
        print(f"收件人: {to_email}")
        print(f"主题: {subject}")
        return True
        
    except Exception as e:
        print(f"邮件发送失败: {str(e)}")
        return False

if __name__ == "__main__":
    send_completion_email()
