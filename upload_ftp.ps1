$ftpServer = "8.138.203.25"
$ftpUser = "diwangzhidao"
$ftpPass = "88888888"
$localFile = "20250804075947-jinrongrumen9jiekecheng.php"
$remoteFile = "/MCP/xiangliang/1/20250804075947-jinrongrumen9jiekecheng.php"

try {
    $ftpRequest = [System.Net.FtpWebRequest]::Create("ftp://$ftpServer$remoteFile")
    $ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::UploadFile
    $ftpRequest.Credentials = New-Object System.Net.NetworkCredential($ftpUser, $ftpPass)
    $ftpRequest.UseBinary = $true
    $ftpRequest.UsePassive = $true

    $fileContent = [System.IO.File]::ReadAllBytes($localFile)
    $ftpRequest.ContentLength = $fileContent.Length

    $requestStream = $ftpRequest.GetRequestStream()
    $requestStream.Write($fileContent, 0, $fileContent.Length)
    $requestStream.Close()

    $response = $ftpRequest.GetResponse()
    Write-Host "上传成功: $($response.StatusDescription)"
    Write-Host "文件大小: $($fileContent.Length) 字节"
    Write-Host "访问地址: https://www.diwangzhidao.com/MCP/xiangliang/1/20250804075947-jinrongrumen9jiekecheng.php"
    $response.Close()
} catch {
    Write-Host "上传失败: $($_.Exception.Message)"
}
