<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金融入门9节零基础课程 - 项老师AI工作室</title>
    <meta name="description" content="系统学习金融知识，9节课从入门到精通，零基础也能轻松掌握投资理财">
    
    <!-- CDN资源 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script defer src="https://cdn.bootcdn.net/ajax/libs/alpinejs/3.12.3/cdn.min.js"></script>
    
    <style>
        /* Loading动画样式 */
        #loading-screen {
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            background-color: #000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        #loading-logo {
            width: 70px; height: 70px;
            margin-bottom: 30px;
            opacity: 0;
            transform: scale(0.8);
            animation: logoFadeIn 1s ease forwards;
            border-radius: 12px;
        }

        #loading-progress {
            width: 180px; height: 3px;
            background-color: rgba(255,255,255,0.2);
            border-radius: 1.5px;
            overflow: hidden;
            position: relative;
            opacity: 0;
            animation: progressFadeIn 0.5s ease 0.5s forwards;
        }

        #loading-bar {
            position: absolute;
            top: 0; left: 0;
            height: 100%; width: 0%;
            background-color: rgba(255,255,255,0.9);
            border-radius: 1.5px;
            transition: width 0.3s ease;
            box-shadow: 0 0 5px rgba(255,255,255,0.7);
        }

        #loading-text {
            margin-top: 15px;
            font-size: 12px;
            color: rgba(255,255,255,0.6);
            text-align: center;
            opacity: 0;
            animation: textFadeIn 0.5s ease 1s forwards;
            transition: opacity 0.3s ease;
            min-height: 16px;
        }

        @keyframes logoFadeIn {
            0% { opacity: 0; transform: scale(0.8); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes progressFadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes textFadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        /* AI风格样式 */
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 粒子背景 */
        .particles {
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px; height: 2px;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-10vh) translateX(50px); opacity: 0; }
        }

        /* 玻璃透明效果 */
        .glass {
            background: linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1), 0 0 20px rgba(59,130,246,0.3);
        }

        /* 发光动画 */
        .glow {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 0 20px rgba(59,130,246,0.4); }
            50% { box-shadow: 0 0 30px rgba(59,130,246,0.8); }
        }

        /* 呼吸动画 */
        .breathe {
            animation: breathe 3s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* 底部固定导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0; left: 0; right: 0;
            height: 70px;
            background: linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255,255,255,0.3);
            z-index: 1000;
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* 移动端导航 */
        .mobile-nav {
            position: fixed;
            top: 0; right: -300px;
            width: 300px; height: 100vh;
            background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(26,26,46,0.9) 100%);
            backdrop-filter: blur(10px);
            transition: right 0.3s ease;
            z-index: 2000;
            padding-top: 60px;
        }

        .mobile-nav.open {
            right: 0;
        }

        /* 课程内容样式 */
        .lesson-content {
            padding-bottom: 100px; /* 为底部导航留空间 */
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .desktop-only { display: none; }
        }

        @media (min-width: 769px) {
            .mobile-only { display: none; }
            .bottom-nav { display: none; }
        }
    </style>
</head>
<body class="text-white">
    <!-- Loading动画 -->
    <div id="loading-screen">
        <img id="loading-logo" src="https://www.diwangzhidao.com/logo.png" alt="项老师Logo">
        <div id="loading-progress">
            <div id="loading-bar"></div>
        </div>
        <div id="loading-text">正在初始化...</div>
    </div>

    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 移动端导航按钮 -->
    <button class="mobile-only fixed top-4 right-4 z-30 glass p-3 rounded-full glow" onclick="toggleMobileNav()">
        <i class="fas fa-bars text-white"></i>
    </button>

    <!-- 移动端侧边导航 -->
    <div class="mobile-nav" id="mobileNav">
        <div class="p-6">
            <div class="flex items-center justify-between mb-8">
                <h3 class="text-xl font-bold text-white">课程目录</h3>
                <button onclick="toggleMobileNav()" class="text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <nav class="space-y-4">
                <a href="#lesson1" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-play-circle mr-2"></i>第1节：金融世界的大门
                </a>
                <a href="#lesson2" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-university mr-2"></i>第2节：货币的故事
                </a>
                <a href="#lesson3" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-chart-line mr-2"></i>第3节：数字背后的秘密
                </a>
                <a href="#lesson4" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-coins mr-2"></i>第4节：投资的第一步
                </a>
                <a href="#lesson5" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-balance-scale mr-2"></i>第5节：投资的智慧
                </a>
                <a href="#lesson6" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-piggy-bank mr-2"></i>第6节：财富管理
                </a>
                <a href="#lesson7" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-chart-area mr-2"></i>第7节：市场的脉搏
                </a>
                <a href="#lesson8" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-robot mr-2"></i>第8节：科技改变金融
                </a>
                <a href="#lesson9" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-road mr-2"></i>第9节：规划未来
                </a>
            </nav>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-8 relative z-10 lesson-content">
        <!-- 课程标题 -->
        <div class="text-center mb-12">
            <div class="inline-block glass p-8 rounded-3xl glow breathe">
                <i class="fas fa-graduation-cap text-6xl text-blue-400 mb-4"></i>
                <h1 class="text-4xl md:text-6xl font-thin mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    金融入门
                </h1>
                <p class="text-xl text-gray-300 tracking-wider">9节零基础课程</p>
            </div>
        </div>

        <!-- 制作信息栏 -->
        <div class="glass p-4 rounded-xl mb-8 text-center">
            <div class="flex flex-wrap justify-center items-center gap-4 text-sm text-gray-300">
                <span>
                    <i class="fas fa-user mr-1"></i>
                    制作者：<a href="#" class="text-blue-400 hover:text-blue-300">项老师超级总裁助理001</a>
                </span>
                <span>
                    <i class="fas fa-clock mr-1"></i>
                    用时：<span id="duration">13分36秒</span>
                </span>
                <span>
                    <i class="fas fa-calendar mr-1"></i>
                    时间：2025-08-04 07:59:47 ~ 2025-08-04 08:13:23
                </span>
            </div>
        </div>

        <!-- 课程内容将在这里动态生成 -->
        <div id="courseContent">
            <!-- 课程内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 底部固定导航栏 -->
    <div class="bottom-nav mobile-only">
        <div class="flex items-center justify-around h-full px-4">
            <button id="prevBtn" class="flex flex-col items-center justify-center p-2 rounded-lg glass hover:bg-blue-500/20 transition-colors" onclick="previousLesson()">
                <i class="fas fa-chevron-left text-xl mb-1"></i>
                <span class="text-xs">上一节</span>
            </button>
            
            <button id="playBtn" class="flex flex-col items-center justify-center p-3 rounded-full glass glow hover:bg-blue-500/20 transition-colors" onclick="toggleAudio()">
                <i class="fas fa-play text-2xl mb-1" id="playIcon"></i>
                <span class="text-xs">播放</span>
            </button>
            
            <button id="nextBtn" class="flex flex-col items-center justify-center p-2 rounded-lg glass hover:bg-blue-500/20 transition-colors" onclick="nextLesson()">
                <i class="fas fa-chevron-right text-xl mb-1"></i>
                <span class="text-xs">下一节</span>
            </button>
        </div>
    </div>

    <script>
        // 全局变量
        let currentLesson = 1;
        let totalLessons = 9;
        let isPlaying = false;
        let currentAudio = null;
        let audioCache = {};

        // Loading管理器
        class RealLoadingManager {
            constructor(options = {}) {
                this.loadingScreen = document.getElementById('loading-screen');
                this.progressBar = document.getElementById('loading-bar');
                this.loadingText = document.getElementById('loading-text');
                this.tasks = [];
                this.completedTasks = 0;
                this.isCompleted = false;
                this.maxWaitTime = options.maxWaitTime || 5000;
                this.minDisplayTime = options.minDisplayTime || 300;
                this.startTime = null;
                this.loadingMessages = [
                    '正在加载核心资源...',
                    '正在初始化样式文件...',
                    '正在加载脚本文件...',
                    '正在获取课程数据...',
                    '正在加载项老师Logo...',
                    '正在连接语音服务...',
                    '正在验证资源完整性...',
                    '即将完成...'
                ];
                this.currentMessageIndex = 0;
            }

            addTask(taskPromise, description = '') {
                this.tasks.push({ promise: taskPromise, description });
                return this;
            }

            updateProgress(progress) {
                if (!this.isCompleted) {
                    this.progressBar.style.width = `${Math.min(100, Math.max(0, progress))}%`;
                }
            }

            updateLoadingText(text) {
                if (this.loadingText && !this.isCompleted) {
                    this.loadingText.style.opacity = '0';
                    setTimeout(() => {
                        if (!this.isCompleted) {
                            this.loadingText.textContent = text;
                            this.loadingText.style.opacity = '1';
                        }
                    }, 150);
                }
            }

            getNextMessage() {
                const message = this.loadingMessages[this.currentMessageIndex];
                this.currentMessageIndex = (this.currentMessageIndex + 1) % this.loadingMessages.length;
                return message;
            }

            async start() {
                this.startTime = Date.now();
                const totalTasks = this.tasks.length;

                this.updateLoadingText(this.getNextMessage());

                const timeoutId = setTimeout(() => {
                    if (!this.isCompleted) {
                        console.warn('加载超时，强制进入界面');
                        this.updateLoadingText('加载超时，正在进入...');
                        this.forceComplete();
                    }
                }, this.maxWaitTime);

                const textUpdateInterval = setInterval(() => {
                    if (!this.isCompleted) {
                        this.updateLoadingText(this.getNextMessage());
                    } else {
                        clearInterval(textUpdateInterval);
                    }
                }, 800);

                this.tasks.forEach((taskObj, index) => {
                    const { promise: task, description } = taskObj;

                    if (description) {
                        this.updateLoadingText(description);
                    }

                    task.finally(() => {
                        if (!this.isCompleted) {
                            this.completedTasks++;
                            const progress = (this.completedTasks / totalTasks) * 100;
                            this.updateProgress(progress);

                            if (progress >= 90) {
                                this.updateLoadingText('即将完成...');
                            } else if (progress >= 70) {
                                this.updateLoadingText('正在完成最后步骤...');
                            }

                            if (this.completedTasks === totalTasks) {
                                clearTimeout(timeoutId);
                                clearInterval(textUpdateInterval);
                                this.updateLoadingText('加载完成');
                                this.complete();
                            }
                        }
                    });
                });

                try {
                    await Promise.race([
                        Promise.allSettled(this.tasks.map(t => t.promise)),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Loading timeout')), this.maxWaitTime)
                        )
                    ]);
                } catch (error) {
                    console.warn('部分资源加载失败或超时:', error.message);
                }

                clearTimeout(timeoutId);
                clearInterval(textUpdateInterval);
                if (!this.isCompleted) {
                    this.complete();
                }
            }

            forceComplete() {
                if (this.isCompleted) return;
                console.log('强制完成加载，进度:', this.completedTasks, '/', this.tasks.length);
                this.updateProgress(100);
                this.complete();
            }

            complete() {
                if (this.isCompleted) return;
                this.isCompleted = true;

                const elapsedTime = Date.now() - this.startTime;
                const remainingMinTime = Math.max(0, this.minDisplayTime - elapsedTime);

                setTimeout(() => {
                    this.loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        this.loadingScreen.style.display = 'none';
                        this.onCompleteCallback?.();
                    }, 500);
                }, remainingMinTime);
            }

            onComplete(callback) {
                this.onCompleteCallback = callback;
                return this;
            }
        }

        // 辅助函数
        function loadCSS(url) {
            return new Promise((resolve, reject) => {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = url;
                link.onload = resolve;
                link.onerror = reject;
                document.head.appendChild(link);
            });
        }

        function loadJS(url) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = url;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        function loadImage(url) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = resolve;
                img.onerror = reject;
                img.src = url;
            });
        }

        // 初始化Loading
        const loader = new RealLoadingManager({
            maxWaitTime: 5000,
            minDisplayTime: 300
        })
            .addTask(loadImage('https://www.diwangzhidao.com/logo.png'), '正在加载项老师Logo...')
            .addTask(new Promise(resolve => setTimeout(resolve, 500)), '正在初始化课程内容...')
            .addTask(new Promise(resolve => setTimeout(resolve, 300)), '正在连接语音服务...')
            .onComplete(() => {
                console.log('加载完成');
                initializeApp();
            });

        // 页面加载完成后启动Loading
        document.addEventListener('DOMContentLoaded', () => {
            loader.start();
        });

        // 初始化应用
        function initializeApp() {
            createParticles();
            loadCourseContent();
            updateNavigationState();
        }

        // 创建粒子背景
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 移动端导航切换
        function toggleMobileNav() {
            const nav = document.getElementById('mobileNav');
            nav.classList.toggle('open');
        }

        // 导航功能
        function previousLesson() {
            if (currentLesson > 1) {
                currentLesson--;
                loadCourseContent();
                updateNavigationState();
            }
        }

        function nextLesson() {
            if (currentLesson < totalLessons) {
                currentLesson++;
                loadCourseContent();
                updateNavigationState();
            }
        }

        function updateNavigationState() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            if (prevBtn) {
                prevBtn.style.opacity = currentLesson > 1 ? '1' : '0.5';
                prevBtn.disabled = currentLesson <= 1;
            }
            
            if (nextBtn) {
                nextBtn.style.opacity = currentLesson < totalLessons ? '1' : '0.5';
                nextBtn.disabled = currentLesson >= totalLessons;
            }
        }

        // 音频播放功能
        function toggleAudio() {
            const playIcon = document.getElementById('playIcon');
            
            if (isPlaying) {
                if (currentAudio) {
                    currentAudio.pause();
                }
                playIcon.className = 'fas fa-play text-2xl mb-1';
                isPlaying = false;
            } else {
                playCurrentLessonAudio();
                playIcon.className = 'fas fa-pause text-2xl mb-1';
                isPlaying = true;
            }
        }

        function playCurrentLessonAudio() {
            // 这里将实现语音播放功能
            console.log('播放第' + currentLesson + '节课程音频');
        }

        // 课程内容数据
        const courseData = {
            1: {
                title: "金融世界的大门 - 什么是金融？",
                icon: "fas fa-door-open",
                objectives: [
                    "理解金融的基本定义和核心概念",
                    "认识金融在日常生活中的重要作用",
                    "了解金融体系的基本构成要素",
                    "掌握金融与经济发展的关系",
                    "建立正确的金融学习观念"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">什么是金融？</h3>
                    <p class="mb-4 leading-relaxed">
                        金融，简单来说就是资金的融通。想象一下，你有多余的钱想要增值，而另一个人需要资金来创业或购房，金融就是连接你们两者的桥梁。金融不仅仅是银行存款和贷款，它涵盖了投资、保险、证券、外汇等各个领域，是现代经济的血液循环系统。
                    </p>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">金融的三大核心功能</h4>
                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-exchange-alt text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">资金融通</h5>
                            <p class="text-sm text-gray-300">将闲置资金从储蓄者转移到需要资金的投资者和消费者手中</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-shield-alt text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">风险管理</h5>
                            <p class="text-sm text-gray-300">通过保险、衍生品等工具帮助个人和企业转移和分散风险</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-credit-card text-2xl text-yellow-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">支付清算</h5>
                            <p class="text-sm text-gray-300">提供便捷的支付手段，促进商品和服务的交换</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">金融与我们的日常生活</h4>
                    <p class="mb-4 leading-relaxed">
                        也许你觉得金融很遥远，但实际上它无处不在。当你用手机支付买咖啡时，这是金融科技的应用；当你把钱存入银行获得利息时，这是最基础的金融投资；当你买房申请贷款时，这是金融服务的体现；当你购买保险保障家庭时，这是金融风险管理的实践。
                    </p>

                    <div class="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-4 rounded-lg mb-6">
                        <h5 class="font-semibold mb-2 text-blue-300"><i class="fas fa-lightbulb mr-2"></i>生活中的金融实例</h5>
                        <ul class="space-y-2 text-sm">
                            <li><i class="fas fa-mobile-alt mr-2 text-green-400"></i>移动支付：微信支付、支付宝改变了我们的消费习惯</li>
                            <li><i class="fas fa-home mr-2 text-blue-400"></i>房屋贷款：让普通家庭能够提前实现住房梦想</li>
                            <li><i class="fas fa-car mr-2 text-yellow-400"></i>汽车金融：分期付款让更多人能够购买汽车</li>
                            <li><i class="fas fa-graduation-cap mr-2 text-purple-400"></i>教育贷款：为求学者提供资金支持</li>
                        </ul>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">金融体系的基本构成</h4>
                    <p class="mb-4 leading-relaxed">
                        金融体系就像一个庞大的生态系统，包含了各种不同的机构和市场。中央银行是这个系统的"大脑"，负责制定货币政策；商业银行是"血管"，负责资金的流通；证券市场是"心脏"，为企业提供直接融资渠道；保险公司是"免疫系统"，帮助社会分散风险。
                    </p>

                    <div class="grid md:grid-cols-2 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-2 text-blue-300">金融机构</h5>
                            <ul class="text-sm space-y-1 text-gray-300">
                                <li>• 中央银行（央行）</li>
                                <li>• 商业银行</li>
                                <li>• 投资银行</li>
                                <li>• 保险公司</li>
                                <li>• 基金公司</li>
                                <li>• 证券公司</li>
                            </ul>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-2 text-green-300">金融市场</h5>
                            <ul class="text-sm space-y-1 text-gray-300">
                                <li>• 货币市场</li>
                                <li>• 资本市场</li>
                                <li>• 外汇市场</li>
                                <li>• 商品期货市场</li>
                                <li>• 保险市场</li>
                                <li>• 金融衍生品市场</li>
                            </ul>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "小王的理财之路",
                        content: "小王刚毕业工作，每月收入8000元。他将3000元存入银行定期存款（银行业务），2000元购买货币基金（基金业务），1000元买了重疾险（保险业务），剩余2000元用于日常开销。通过这些金融工具，小王实现了资金的保值增值和风险保障。"
                    },
                    {
                        title: "创业公司的融资历程",
                        content: "张总的科技公司需要500万资金扩大生产。他首先向银行申请了200万贷款（间接融资），然后通过股权投资获得了300万资金（直接融资）。这个过程展示了金融如何支持实体经济发展，帮助企业获得发展所需的资金。"
                    }
                ],
                summary: [
                    "金融是资金融通的总称，具有资金融通、风险管理、支付清算三大核心功能",
                    "金融与我们的日常生活密切相关，从移动支付到房贷车贷都离不开金融服务",
                    "金融体系由金融机构和金融市场构成，各司其职又相互配合",
                    "理解金融有助于我们更好地管理个人财务，实现财富增值",
                    "金融是现代经济的重要组成部分，推动着社会经济的发展"
                ],
                thinking: [
                    "回想一下你今天使用了哪些金融服务？（如移动支付、银行卡等）",
                    "如果你有10万元闲钱，你会选择哪种金融工具来管理？为什么？",
                    "观察你身边的人，他们是如何利用金融工具来改善生活的？"
                ],
                nextPreview: "下一节我们将深入了解货币的发展历史和银行体系的运作机制，探索金融世界的基础设施。"
            },
            2: {
                title: "货币的故事 - 银行与货币体系",
                icon: "fas fa-university",
                objectives: [
                    "了解货币的发展历史和基本功能",
                    "理解银行体系的运作机制",
                    "掌握央行与商业银行的区别和联系",
                    "认识现代货币政策的基本工具",
                    "理解货币在经济中的重要作用"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">货币的演进历程</h3>
                    <p class="mb-4 leading-relaxed">
                        货币的历史就是人类文明发展的缩影。从最初的以物易物，到贝壳、金银，再到现代的纸币和数字货币，每一次货币形态的变化都反映了社会生产力的进步。货币不仅仅是交换媒介，更是价值尺度、储藏手段和支付工具。
                    </p>

                    <div class="grid md:grid-cols-4 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-handshake text-3xl text-yellow-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">以物易物</h5>
                            <p class="text-xs text-gray-300">原始社会的直接交换</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-coins text-3xl text-orange-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">商品货币</h5>
                            <p class="text-xs text-gray-300">贝壳、金银等实物货币</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-money-bill text-3xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">纸币时代</h5>
                            <p class="text-xs text-gray-300">政府发行的法定货币</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-mobile-alt text-3xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">数字货币</h5>
                            <p class="text-xs text-gray-300">电子支付和数字人民币</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">货币的四大基本功能</h4>
                    <div class="grid md:grid-cols-2 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-2 text-blue-300"><i class="fas fa-exchange-alt mr-2"></i>交换媒介</h5>
                            <p class="text-sm text-gray-300">货币作为中介，使商品交换变得简单高效，避免了以物易物的复杂性</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-2 text-green-300"><i class="fas fa-ruler mr-2"></i>价值尺度</h5>
                            <p class="text-sm text-gray-300">货币为所有商品和服务提供统一的价值衡量标准</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-2 text-yellow-300"><i class="fas fa-piggy-bank mr-2"></i>储藏手段</h5>
                            <p class="text-sm text-gray-300">货币可以保存价值，让人们能够为未来储蓄财富</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-2 text-purple-300"><i class="fas fa-credit-card mr-2"></i>支付工具</h5>
                            <p class="text-sm text-gray-300">货币可以延期支付，支持信贷和分期付款</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">银行体系的层次结构</h4>
                    <p class="mb-4 leading-relaxed">
                        现代银行体系是一个分层的金融网络。中央银行位于顶层，是"银行的银行"，负责制定货币政策和监管金融体系；商业银行是中间层，直接为企业和个人提供金融服务；政策性银行和其他金融机构则在特定领域发挥作用。
                    </p>

                    <div class="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-blue-300"><i class="fas fa-sitemap mr-2"></i>中国银行体系结构</h5>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                <span class="font-medium">中央银行：</span>
                                <span class="text-gray-300">中国人民银行（央行）</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                                <span class="font-medium">政策性银行：</span>
                                <span class="text-gray-300">国开行、进出口银行、农发行</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                <span class="font-medium">国有大型银行：</span>
                                <span class="text-gray-300">工农中建交邮储六大行</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                <span class="font-medium">股份制银行：</span>
                                <span class="text-gray-300">招商、浦发、中信等</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-purple-400 rounded-full"></div>
                                <span class="font-medium">地方银行：</span>
                                <span class="text-gray-300">城商行、农商行、村镇银行</span>
                            </div>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">央行的货币政策工具</h4>
                    <p class="mb-4 leading-relaxed">
                        中央银行通过货币政策来调节经济运行，主要工具包括存款准备金率、利率政策和公开市场操作。这些工具就像汽车的油门、刹车和方向盘，帮助央行调控货币供应量，维护金融稳定。
                    </p>

                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-percentage text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">存款准备金率</h5>
                            <p class="text-sm text-gray-300">银行必须向央行上缴的资金比例，调节银行放贷能力</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-chart-line text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">利率政策</h5>
                            <p class="text-sm text-gray-300">通过调整基准利率影响整个社会的借贷成本</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-exchange-alt text-2xl text-yellow-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">公开市场操作</h5>
                            <p class="text-sm text-gray-300">央行在公开市场买卖债券，调节市场流动性</p>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "移动支付改变生活",
                        content: "李阿姨是一位60岁的退休教师，起初对手机支付很抗拒。但在女儿的帮助下学会了微信支付后，她发现买菜、坐公交、缴费都变得非常方便。这个例子说明了货币形态的演进如何改变我们的生活方式，数字货币正在成为新的支付主流。"
                    },
                    {
                        title: "央行降准的连锁反应",
                        content: "2023年某次央行宣布降准0.5个百分点，释放长期资金约1万亿元。这意味着银行可用于放贷的资金增加，市场流动性改善，企业更容易获得贷款，股市也因此上涨。这个例子展示了央行货币政策如何通过银行体系影响整个经济。"
                    }
                ],
                summary: [
                    "货币经历了从以物易物到数字货币的演进过程，功能不断完善",
                    "货币具有交换媒介、价值尺度、储藏手段、支付工具四大基本功能",
                    "银行体系分为央行、政策性银行、商业银行等不同层次",
                    "央行通过存准率、利率、公开市场操作等工具调节货币供应量",
                    "理解货币和银行体系有助于我们更好地进行个人财务规划"
                ],
                thinking: [
                    "观察你一天的支付行为，现金、银行卡、移动支付各占多少比例？",
                    "如果央行提高利率，对你的储蓄和贷款会产生什么影响？",
                    "想想你使用的银行属于哪个类型？它们的服务有什么特点？"
                ],
                nextPreview: "下一节我们将学习利率和通胀这两个重要的金融指标，了解它们如何影响我们的投资和消费决策。"
            },
            3: {
                title: "数字背后的秘密 - 利率与通胀",
                icon: "fas fa-chart-line",
                objectives: [
                    "理解利率的基本概念和影响因素",
                    "掌握通胀的成因和经济影响",
                    "学会分析利率和通胀的关系",
                    "了解如何应对通胀保护财富",
                    "掌握利率变化对投资的影响"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">利率：资金的价格</h3>
                    <p class="mb-4 leading-relaxed">
                        利率就是使用资金的价格，就像租房要付房租一样，借钱也要付"租金"。利率不仅影响我们的储蓄收益和贷款成本，更是整个经济运行的重要指标。当央行调整利率时，会像多米诺骨牌一样影响整个社会的经济活动。
                    </p>

                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-piggy-bank text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">存款利率</h5>
                            <p class="text-sm text-gray-300">银行给储户的回报，鼓励人们储蓄</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-hand-holding-usd text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">贷款利率</h5>
                            <p class="text-sm text-gray-300">借款人需要支付的成本，影响投资和消费</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-university text-2xl text-purple-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">基准利率</h5>
                            <p class="text-sm text-gray-300">央行设定的政策利率，影响整个利率体系</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">影响利率的主要因素</h4>
                    <div class="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-6 rounded-lg mb-6">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="font-semibold mb-3 text-blue-300">宏观因素</h5>
                                <ul class="space-y-2 text-sm text-gray-300">
                                    <li><i class="fas fa-chart-line mr-2 text-green-400"></i>经济增长速度</li>
                                    <li><i class="fas fa-fire mr-2 text-red-400"></i>通胀水平</li>
                                    <li><i class="fas fa-balance-scale mr-2 text-blue-400"></i>货币政策</li>
                                    <li><i class="fas fa-globe mr-2 text-purple-400"></i>国际资本流动</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-semibold mb-3 text-green-300">微观因素</h5>
                                <ul class="space-y-2 text-sm text-gray-300">
                                    <li><i class="fas fa-shield-alt mr-2 text-yellow-400"></i>信用风险</li>
                                    <li><i class="fas fa-clock mr-2 text-orange-400"></i>期限长短</li>
                                    <li><i class="fas fa-water mr-2 text-cyan-400"></i>流动性需求</li>
                                    <li><i class="fas fa-handshake mr-2 text-pink-400"></i>供求关系</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <h3 class="text-2xl font-bold mb-4 text-blue-400">通胀：货币购买力的侵蚀</h3>
                    <p class="mb-4 leading-relaxed">
                        通胀就是物价普遍上涨，货币购买力下降的现象。简单说，就是同样的钱能买到的东西越来越少了。适度的通胀（年率2-3%）是健康的，说明经济在增长；但过高的通胀会侵蚀财富，过低或通缩则可能导致经济停滞。
                    </p>

                    <div class="grid md:grid-cols-4 gap-3 mb-6">
                        <div class="glass p-3 rounded-lg text-center">
                            <div class="text-2xl mb-2">😊</div>
                            <h6 class="font-semibold text-green-400">温和通胀</h6>
                            <p class="text-xs text-gray-300">2-3%</p>
                            <p class="text-xs text-gray-300">经济健康</p>
                        </div>
                        <div class="glass p-3 rounded-lg text-center">
                            <div class="text-2xl mb-2">😐</div>
                            <h6 class="font-semibold text-yellow-400">中度通胀</h6>
                            <p class="text-xs text-gray-300">3-6%</p>
                            <p class="text-xs text-gray-300">需要关注</p>
                        </div>
                        <div class="glass p-3 rounded-lg text-center">
                            <div class="text-2xl mb-2">😰</div>
                            <h6 class="font-semibold text-orange-400">高通胀</h6>
                            <p class="text-xs text-gray-300">6-10%</p>
                            <p class="text-xs text-gray-300">经济过热</p>
                        </div>
                        <div class="glass p-3 rounded-lg text-center">
                            <div class="text-2xl mb-2">😱</div>
                            <h6 class="font-semibold text-red-400">恶性通胀</h6>
                            <p class="text-xs text-gray-300">>10%</p>
                            <p class="text-xs text-gray-300">经济危机</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">通胀的成因分析</h4>
                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-shopping-cart text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">需求拉动型</h5>
                            <p class="text-sm text-gray-300">社会总需求超过总供给，"钱多货少"推高物价</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-industry text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">成本推动型</h5>
                            <p class="text-sm text-gray-300">原材料、人工等成本上升，推高商品价格</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-brain text-2xl text-purple-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">预期推动型</h5>
                            <p class="text-sm text-gray-300">人们预期物价上涨，提前消费形成自我实现</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">利率与通胀的关系</h4>
                    <p class="mb-4 leading-relaxed">
                        利率和通胀就像跷跷板的两端，存在着复杂的相互影响关系。一般来说，央行会通过调整利率来控制通胀：加息抑制通胀，降息刺激经济。但这种关系并非绝对，还要考虑经济周期、国际环境等多种因素。
                    </p>

                    <div class="bg-gradient-to-r from-green-500/20 to-blue-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-green-300"><i class="fas fa-lightbulb mr-2"></i>抗通胀投资策略</h5>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h6 class="font-medium mb-2 text-blue-300">实物资产</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 房地产：保值增值的传统选择</li>
                                    <li>• 黄金：避险保值的贵金属</li>
                                    <li>• 大宗商品：原材料价格跟随通胀</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-purple-300">金融资产</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 股票：优质企业能够转嫁成本</li>
                                    <li>• 通胀保护债券：收益率与通胀挂钩</li>
                                    <li>• 浮动利率产品：利率上升时收益增加</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "房贷利率变化的影响",
                        content: "小张在2020年以LPR+0.5%的利率贷款买房，当时5年期LPR为4.65%，月供约5500元。2023年由于经济形势变化，LPR降至4.2%，他的月供减少到约5200元，每月节省300元。这个例子说明了利率变化对个人财务的直接影响。"
                    },
                    {
                        title: "通胀对储蓄的侵蚀",
                        content: "王阿姨在2020年有10万元存款，当时一斤猪肉30元，可以买3333斤。如果年通胀率为3%，三年后这10万元的购买力相当于原来的9.1万元，只能买3030斤猪肉了。这说明了通胀如何悄无声息地侵蚀财富，储蓄需要跑赢通胀才能保值。"
                    }
                ],
                summary: [
                    "利率是资金的价格，受宏观经济和微观因素双重影响",
                    "通胀是物价普遍上涨现象，适度通胀有利于经济发展",
                    "央行通过调整利率来调控通胀，两者存在复杂的相互关系",
                    "投资者需要选择能够抗通胀的资产来保护财富",
                    "理解利率和通胀有助于做出更明智的财务决策"
                ],
                thinking: [
                    "计算一下你的储蓄收益率是否跑赢了通胀？",
                    "如果央行加息，你会如何调整自己的投资组合？",
                    "观察身边商品价格变化，分析可能的通胀原因"
                ],
                nextPreview: "下一节我们将进入投资世界，学习股票、债券、基金等基本投资工具的特点和风险。"
            },
            4: {
                title: "投资的第一步 - 股票、债券、基金入门",
                icon: "fas fa-coins",
                objectives: [
                    "理解股票的基本概念和投资特点",
                    "掌握债券的安全性和收益特征",
                    "了解基金的分散投资优势",
                    "学会比较不同投资工具的风险收益",
                    "建立正确的投资理念和心态"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">股票：企业所有权的凭证</h3>
                    <p class="mb-4 leading-relaxed">
                        股票就是公司的"股份证明书"，买股票就是成为公司的股东，享有公司的部分所有权。当公司盈利时，股东可以获得分红；当公司价值增长时，股票价格也会上涨。但同时，如果公司经营不善，股票价格也可能下跌，投资者面临亏损风险。
                    </p>

                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-chart-line text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">资本利得</h5>
                            <p class="text-sm text-gray-300">股票价格上涨带来的收益，是股票投资的主要收益来源</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-gift text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">股息分红</h5>
                            <p class="text-sm text-gray-300">公司盈利后向股东分配的现金或股票</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-vote-yea text-2xl text-purple-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">股东权利</h5>
                            <p class="text-sm text-gray-300">参与公司重大决策的投票权</p>
                        </div>
                    </div>

                    <h3 class="text-2xl font-bold mb-4 text-blue-400">债券：稳健的固定收益投资</h3>
                    <p class="mb-4 leading-relaxed">
                        债券就是"借条"，政府或企业向投资者借钱，承诺在约定时间还本付息。债券的风险通常比股票低，收益也相对稳定，适合追求稳健收益的投资者。债券的安全性主要取决于发行方的信用等级。
                    </p>

                    <div class="bg-gradient-to-r from-blue-500/20 to-green-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-blue-300"><i class="fas fa-layer-group mr-2"></i>债券分类</h5>
                        <div class="grid md:grid-cols-3 gap-4">
                            <div>
                                <h6 class="font-medium mb-2 text-green-300">按发行主体</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 国债：政府发行，最安全</li>
                                    <li>• 地方债：地方政府发行</li>
                                    <li>• 企业债：公司发行，收益较高</li>
                                    <li>• 金融债：银行等金融机构发行</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-blue-300">按期限长短</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 短期债券：1年以内</li>
                                    <li>• 中期债券：1-10年</li>
                                    <li>• 长期债券：10年以上</li>
                                    <li>• 永续债券：无到期日</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-purple-300">按利率类型</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 固定利率债券</li>
                                    <li>• 浮动利率债券</li>
                                    <li>• 零息债券</li>
                                    <li>• 通胀保护债券</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <h3 class="text-2xl font-bold mb-4 text-blue-400">基金：专业理财的智慧选择</h3>
                    <p class="mb-4 leading-relaxed">
                        基金就是把很多投资者的钱集中起来，交给专业的基金经理去投资管理。这样既能享受专业投资管理，又能通过分散投资降低风险，还能以较小的资金参与到原本门槛较高的投资领域。基金是普通投资者参与金融市场的重要工具。
                    </p>

                    <div class="grid md:grid-cols-4 gap-3 mb-6">
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-chart-area text-2xl text-blue-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">股票基金</h6>
                            <p class="text-xs text-gray-300">主要投资股票</p>
                            <p class="text-xs text-yellow-300">高风险高收益</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-certificate text-2xl text-green-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">债券基金</h6>
                            <p class="text-xs text-gray-300">主要投资债券</p>
                            <p class="text-xs text-green-300">低风险稳收益</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-balance-scale text-2xl text-purple-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">混合基金</h6>
                            <p class="text-xs text-gray-300">股债混合投资</p>
                            <p class="text-xs text-blue-300">中等风险收益</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-coins text-2xl text-yellow-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">货币基金</h6>
                            <p class="text-xs text-gray-300">投资短期工具</p>
                            <p class="text-xs text-gray-300">极低风险</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">投资工具对比分析</h4>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-gray-600">
                                    <th class="text-left p-3 text-blue-300">投资工具</th>
                                    <th class="text-left p-3 text-green-300">风险等级</th>
                                    <th class="text-left p-3 text-yellow-300">预期收益</th>
                                    <th class="text-left p-3 text-purple-300">流动性</th>
                                    <th class="text-left p-3 text-pink-300">投资门槛</th>
                                </tr>
                            </thead>
                            <tbody class="text-gray-300">
                                <tr class="border-b border-gray-700">
                                    <td class="p-3">股票</td>
                                    <td class="p-3">高</td>
                                    <td class="p-3">8-15%</td>
                                    <td class="p-3">高</td>
                                    <td class="p-3">100元起</td>
                                </tr>
                                <tr class="border-b border-gray-700">
                                    <td class="p-3">债券</td>
                                    <td class="p-3">低-中</td>
                                    <td class="p-3">3-6%</td>
                                    <td class="p-3">中</td>
                                    <td class="p-3">1000元起</td>
                                </tr>
                                <tr class="border-b border-gray-700">
                                    <td class="p-3">股票基金</td>
                                    <td class="p-3">中-高</td>
                                    <td class="p-3">6-12%</td>
                                    <td class="p-3">高</td>
                                    <td class="p-3">10元起</td>
                                </tr>
                                <tr class="border-b border-gray-700">
                                    <td class="p-3">债券基金</td>
                                    <td class="p-3">低</td>
                                    <td class="p-3">3-5%</td>
                                    <td class="p-3">高</td>
                                    <td class="p-3">10元起</td>
                                </tr>
                                <tr>
                                    <td class="p-3">货币基金</td>
                                    <td class="p-3">极低</td>
                                    <td class="p-3">2-3%</td>
                                    <td class="p-3">极高</td>
                                    <td class="p-3">1元起</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `,
                examples: [
                    {
                        title: "小李的基金定投之路",
                        content: "小李每月定投1000元到沪深300指数基金，坚持了3年。虽然期间经历了市场波动，但通过定投平均成本，最终获得了年化8%的收益。这个例子说明了基金定投如何帮助普通投资者参与股市，通过时间分散风险。"
                    },
                    {
                        title: "退休阿姨的债券投资",
                        content: "刘阿姨退休后有50万积蓄，考虑到年龄和风险承受能力，她选择购买国债和高等级企业债，年收益率约4%。虽然收益不如股票，但本金安全，收益稳定，很适合她的需求。这体现了债券作为稳健投资工具的价值。"
                    }
                ],
                summary: [
                    "股票代表企业所有权，收益来源于股价上涨和分红，风险较高",
                    "债券是固定收益投资工具，安全性高但收益相对较低",
                    "基金通过专业管理和分散投资，适合普通投资者参与",
                    "不同投资工具有不同的风险收益特征，需要根据自身情况选择",
                    "投资需要长期坚持，通过时间和分散化来降低风险"
                ],
                thinking: [
                    "根据你的年龄和风险承受能力，你更适合哪种投资工具？",
                    "如果你有10万元，你会如何在股票、债券、基金之间分配？",
                    "观察身边投资成功的人，他们主要投资什么产品？"
                ],
                nextPreview: "下一节我们将深入学习风险与收益的关系，掌握投资中最重要的平衡艺术。"
            },
            5: {
                title: "投资的智慧 - 风险与收益的平衡",
                icon: "fas fa-balance-scale",
                objectives: [
                    "理解风险与收益的基本关系",
                    "学会识别和评估投资风险",
                    "掌握分散投资的重要性",
                    "了解风险管理的基本方法",
                    "建立适合自己的风险偏好"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">风险与收益：投资的永恒主题</h3>
                    <p class="mb-4 leading-relaxed">
                        投资世界有一个铁律：高收益必然伴随高风险，低风险只能获得低收益。这就像爬山一样，想要看到更美的风景（高收益），就必须承担更大的攀登风险。理性的投资者不是要消除风险，而是要在风险和收益之间找到最适合自己的平衡点。
                    </p>

                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-shield-alt text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">系统性风险</h5>
                            <p class="text-sm text-gray-300">影响整个市场的风险，如经济衰退、政策变化等，无法通过分散投资消除</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-building text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">非系统性风险</h5>
                            <p class="text-sm text-gray-300">特定公司或行业的风险，如管理层变动、产品问题等，可以通过分散投资降低</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-water text-2xl text-purple-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">流动性风险</h5>
                            <p class="text-sm text-gray-300">无法及时以合理价格卖出投资品的风险</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">分散投资：不要把鸡蛋放在一个篮子里</h4>
                    <p class="mb-4 leading-relaxed">
                        分散投资是风险管理的核心策略。通过投资不同的资产类别、不同的行业、不同的地区，可以有效降低投资组合的整体风险。当某些投资表现不佳时，其他投资可能表现良好，从而平滑整体收益。
                    </p>

                    <div class="bg-gradient-to-r from-purple-500/20 to-blue-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-purple-300"><i class="fas fa-layer-group mr-2"></i>分散投资的维度</h5>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h6 class="font-medium mb-2 text-blue-300">资产类别分散</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 股票：成长性资产</li>
                                    <li>• 债券：稳定收益资产</li>
                                    <li>• 房地产：实物资产</li>
                                    <li>• 商品：通胀对冲资产</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-green-300">行业地区分散</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 不同行业：科技、金融、消费等</li>
                                    <li>• 不同市场：A股、港股、美股</li>
                                    <li>• 不同规模：大盘股、中小盘股</li>
                                    <li>• 不同风格：价值股、成长股</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "2008年金融危机的启示",
                        content: "2008年金融危机时，全球股市大跌，但黄金价格却上涨了25%。那些在投资组合中配置了黄金的投资者，损失相对较小。这个例子说明了资产配置的重要性，不同资产在不同市场环境下的表现差异很大。"
                    },
                    {
                        title: "定投基金的风险分散",
                        content: "张先生从2018年开始定投某只股票基金，期间经历了2018年的大跌、2019年的反弹、2020年的疫情冲击和2021年的结构性行情。通过定期投资，他的平均成本得到了很好的控制，最终获得了不错的收益。"
                    }
                ],
                summary: [
                    "风险与收益成正比，高收益必然伴随高风险",
                    "投资风险分为系统性风险和非系统性风险",
                    "分散投资是降低风险的有效方法",
                    "投资者应根据自身情况确定合适的风险偏好",
                    "风险管理比追求高收益更重要"
                ],
                thinking: [
                    "评估一下你的风险承受能力，属于保守型、平衡型还是激进型？",
                    "如果你的投资组合只有一只股票，你会如何改进？",
                    "想想2020年疫情期间，哪些资产表现较好，哪些表现较差？"
                ],
                nextPreview: "下一节我们将学习个人理财规划，掌握如何管理自己的财务，实现财务自由。"
            },
            6: {
                title: "财富管理 - 个人理财规划",
                icon: "fas fa-piggy-bank",
                objectives: [
                    "学会分析个人财务状况",
                    "掌握理财目标设定的方法",
                    "了解不同理财工具的应用",
                    "建立完整的理财规划体系",
                    "培养良好的理财习惯"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">个人财务状况分析</h3>
                    <p class="mb-4 leading-relaxed">
                        理财的第一步是了解自己的财务状况。就像体检一样，我们需要全面检查自己的收入、支出、资产和负债情况。只有清楚地知道自己的财务现状，才能制定合适的理财计划。
                    </p>

                    <div class="grid md:grid-cols-2 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-3 text-green-300"><i class="fas fa-plus-circle mr-2"></i>资产负债表</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-300">现金及存款</span>
                                    <span class="text-green-400">+</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">投资资产</span>
                                    <span class="text-green-400">+</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">房产车产</span>
                                    <span class="text-green-400">+</span>
                                </div>
                                <div class="flex justify-between border-t border-gray-600 pt-2">
                                    <span class="text-gray-300">房贷车贷</span>
                                    <span class="text-red-400">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">信用卡债务</span>
                                    <span class="text-red-400">-</span>
                                </div>
                                <div class="flex justify-between border-t border-gray-600 pt-2 font-semibold">
                                    <span>净资产</span>
                                    <span class="text-blue-400">=</span>
                                </div>
                            </div>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <h5 class="font-semibold mb-3 text-blue-300"><i class="fas fa-chart-pie mr-2"></i>收支损益表</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-300">工资收入</span>
                                    <span class="text-green-400">+</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">投资收入</span>
                                    <span class="text-green-400">+</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">其他收入</span>
                                    <span class="text-green-400">+</span>
                                </div>
                                <div class="flex justify-between border-t border-gray-600 pt-2">
                                    <span class="text-gray-300">生活支出</span>
                                    <span class="text-red-400">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-300">房贷还款</span>
                                    <span class="text-red-400">-</span>
                                </div>
                                <div class="flex justify-between border-t border-gray-600 pt-2 font-semibold">
                                    <span>可投资资金</span>
                                    <span class="text-blue-400">=</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">理财目标的设定</h4>
                    <p class="mb-4 leading-relaxed">
                        理财目标要具体、可衡量、可实现、相关性强、有时间限制（SMART原则）。比如"我要变富"就不是一个好目标，而"我要在5年内积累100万元用于购房首付"就是一个明确的理财目标。
                    </p>

                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-clock text-2xl text-yellow-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">短期目标</h5>
                            <p class="text-sm text-gray-300 mb-2">1年内</p>
                            <ul class="text-xs space-y-1 text-gray-400">
                                <li>• 建立应急基金</li>
                                <li>• 偿还高息债务</li>
                                <li>• 购买保险</li>
                            </ul>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-calendar-alt text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">中期目标</h5>
                            <p class="text-sm text-gray-300 mb-2">1-5年</p>
                            <ul class="text-xs space-y-1 text-gray-400">
                                <li>• 购房首付</li>
                                <li>• 子女教育金</li>
                                <li>• 创业资金</li>
                            </ul>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-mountain text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">长期目标</h5>
                            <p class="text-sm text-gray-300 mb-2">5年以上</p>
                            <ul class="text-xs space-y-1 text-gray-400">
                                <li>• 退休养老金</li>
                                <li>• 财务自由</li>
                                <li>• 遗产规划</li>
                            </ul>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "小王的理财规划",
                        content: "小王25岁，月收入1万元，目标是30岁前买房。他制定了详细计划：每月储蓄4000元，其中2000元定投基金，1000元存定期，1000元买货币基金作应急资金。通过5年的坚持，他成功积累了购房首付。"
                    },
                    {
                        title: "中年夫妇的财务规划",
                        content: "李先生夫妇40岁，有一个10岁的孩子。他们的理财目标包括：子女教育金50万、退休养老金300万。他们采用了分层投资策略：教育金用稳健的债券基金，养老金用股票基金，并购买了充足的保险保障。"
                    }
                ],
                summary: [
                    "理财前要全面分析个人财务状况，包括资产负债和收支情况",
                    "理财目标要遵循SMART原则，具体明确且可实现",
                    "不同期限的目标需要采用不同的投资策略",
                    "理财是一个长期过程，需要持续监控和调整",
                    "良好的理财习惯比选择投资产品更重要"
                ],
                thinking: [
                    "列出你的资产负债表，计算一下你的净资产是多少？",
                    "设定一个具体的理财目标，并制定实现计划",
                    "分析你的支出结构，哪些是必要的，哪些可以优化？"
                ],
                nextPreview: "下一节我们将探索金融市场的运作机制，了解股市、债市、外汇市场的基本知识。"
            },
            7: {
                title: "市场的脉搏 - 金融市场简介",
                icon: "fas fa-chart-area",
                objectives: [
                    "了解金融市场的基本构成",
                    "理解股票市场的运作机制",
                    "认识债券市场和外汇市场",
                    "学会解读基本的市场指标",
                    "掌握市场信息的获取方法"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">金融市场的生态系统</h3>
                    <p class="mb-4 leading-relaxed">
                        金融市场就像一个巨大的生态系统，各种参与者在这里进行资金的配置和交易。投资者、企业、政府、金融机构都是这个生态系统的重要组成部分。理解市场的运作机制，有助于我们做出更明智的投资决策。
                    </p>

                    <div class="grid md:grid-cols-4 gap-3 mb-6">
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-chart-line text-2xl text-blue-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">股票市场</h6>
                            <p class="text-xs text-gray-300">企业股权交易</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-certificate text-2xl text-green-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">债券市场</h6>
                            <p class="text-xs text-gray-300">债务工具交易</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-exchange-alt text-2xl text-yellow-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">外汇市场</h6>
                            <p class="text-xs text-gray-300">货币兑换交易</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-seedling text-2xl text-purple-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">商品市场</h6>
                            <p class="text-xs text-gray-300">大宗商品交易</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">股票市场深度解析</h4>
                    <p class="mb-4 leading-relaxed">
                        股票市场是企业融资和投资者投资的重要平台。在中国，主要有上海证券交易所、深圳证券交易所和北京证券交易所。市场通过价格发现机制，反映企业的价值和投资者的预期。
                    </p>

                    <div class="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-blue-300"><i class="fas fa-chart-bar mr-2"></i>重要市场指标</h5>
                        <div class="grid md:grid-cols-3 gap-4">
                            <div>
                                <h6 class="font-medium mb-2 text-green-300">价格指标</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 股价：反映市场对企业价值的认知</li>
                                    <li>• 涨跌幅：价格变动的百分比</li>
                                    <li>• 市盈率：股价与每股收益的比值</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-blue-300">交易指标</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 成交量：交易的股票数量</li>
                                    <li>• 成交额：交易的资金总额</li>
                                    <li>• 换手率：成交量与流通股本的比率</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-purple-300">市场指标</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 上证指数：反映上海股市整体走势</li>
                                    <li>• 深证成指：反映深圳股市表现</li>
                                    <li>• 创业板指：反映创业板市场情况</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "2015年股市波动的启示",
                        content: "2015年中国股市经历了大起大落，上证指数从年初的3000点涨到6月的5000多点，然后又快速回落。这次波动让很多投资者认识到市场风险的重要性，也体现了情绪对市场的巨大影响。"
                    },
                    {
                        title: "美元指数对全球市场的影响",
                        content: "2022年美联储加息导致美元指数大幅上涨，全球资金回流美国，新兴市场货币普遍贬值，股市也受到冲击。这个例子说明了全球金融市场的联动性，以及汇率变化对投资的影响。"
                    }
                ],
                summary: [
                    "金融市场包括股票、债券、外汇、商品等多个子市场",
                    "股票市场通过价格发现机制反映企业价值和市场预期",
                    "市场指标是分析市场状况的重要工具",
                    "全球金融市场高度关联，需要关注国际因素",
                    "理解市场机制有助于做出更理性的投资决策"
                ],
                thinking: [
                    "观察最近一周的股市表现，分析可能的影响因素",
                    "比较不同市场指数的表现，思考背后的原因",
                    "关注一只你感兴趣的股票，分析其价格变化"
                ],
                nextPreview: "下一节我们将了解金融科技的发展，看看科技如何改变传统金融服务。"
            },
            8: {
                title: "科技改变金融 - 金融科技的应用",
                icon: "fas fa-robot",
                objectives: [
                    "了解金融科技的发展历程",
                    "认识数字支付的便利性和安全性",
                    "理解网络银行的服务模式",
                    "初步了解区块链技术的应用",
                    "展望金融科技的未来发展"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">金融科技的革命浪潮</h3>
                    <p class="mb-4 leading-relaxed">
                        金融科技（FinTech）正在深刻改变着金融服务的面貌。从移动支付到网络银行，从智能投顾到区块链，科技让金融服务变得更加便民、高效、普惠。中国在移动支付、数字货币等领域已经走在了世界前列。
                    </p>

                    <div class="grid md:grid-cols-4 gap-3 mb-6">
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-mobile-alt text-2xl text-blue-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">移动支付</h6>
                            <p class="text-xs text-gray-300">随时随地支付</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-laptop text-2xl text-green-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">网络银行</h6>
                            <p class="text-xs text-gray-300">线上金融服务</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-brain text-2xl text-purple-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">智能投顾</h6>
                            <p class="text-xs text-gray-300">AI理财建议</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-link text-2xl text-yellow-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">区块链</h6>
                            <p class="text-xs text-gray-300">分布式账本</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">数字支付的普及应用</h4>
                    <p class="mb-4 leading-relaxed">
                        中国的移动支付普及率已经超过80%，从买菜到打车，从缴费到转账，手机支付已经成为生活的一部分。数字人民币的推出，更是将数字支付推向了新的高度，具有法定货币的地位和更强的安全性。
                    </p>

                    <div class="bg-gradient-to-r from-green-500/20 to-blue-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-green-300"><i class="fas fa-shield-alt mr-2"></i>数字支付的安全保障</h5>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h6 class="font-medium mb-2 text-blue-300">技术保障</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 加密技术：保护交易数据安全</li>
                                    <li>• 生物识别：指纹、面部识别验证</li>
                                    <li>• 风控系统：实时监控异常交易</li>
                                    <li>• 多重验证：密码+短信+生物识别</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-purple-300">制度保障</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 监管框架：央行等部门严格监管</li>
                                    <li>• 资金托管：客户资金专户管理</li>
                                    <li>• 赔付机制：损失补偿保障</li>
                                    <li>• 法律保护：相关法律法规完善</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">区块链技术的金融应用</h4>
                    <p class="mb-4 leading-relaxed">
                        区块链技术具有去中心化、不可篡改、透明可追溯的特点，在金融领域有着广泛的应用前景。从数字货币到供应链金融，从跨境支付到智能合约，区块链正在重塑金融服务的基础设施。
                    </p>

                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-coins text-2xl text-yellow-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">数字货币</h5>
                            <p class="text-sm text-gray-300">央行数字货币（CBDC）提供更安全便捷的支付方式</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-handshake text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">智能合约</h5>
                            <p class="text-sm text-gray-300">自动执行的合约，减少中介成本和纠纷</p>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-globe text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">跨境支付</h5>
                            <p class="text-sm text-gray-300">降低跨境转账成本，提高效率</p>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "支付宝的发展历程",
                        content: "支付宝从2004年为淘宝交易提供担保服务开始，发展成为全球领先的数字支付平台。它不仅解决了网购的信任问题，还拓展到线下支付、理财、保险等多个领域，成为金融科技发展的典型案例。"
                    },
                    {
                        title: "数字人民币的试点应用",
                        content: "数字人民币在深圳、苏州等地进行试点，市民可以用数字钱包在商店消费、缴纳公共事业费等。与微信、支付宝不同，数字人民币是法定货币，具有更强的法律地位和更广的接受度。"
                    }
                ],
                summary: [
                    "金融科技正在深刻改变传统金融服务模式",
                    "移动支付已成为中国金融科技的亮丽名片",
                    "区块链技术为金融创新提供了新的技术基础",
                    "金融科技提高了金融服务的效率和普惠性",
                    "技术发展需要与监管政策相协调"
                ],
                thinking: [
                    "统计一下你一天使用了多少次移动支付？",
                    "比较传统银行服务和网络银行服务的差异",
                    "思考区块链技术还可能在哪些金融场景中应用？"
                ],
                nextPreview: "最后一节我们将学习如何制定个人金融规划，实现财务目标和财务自由。"
            },
            9: {
                title: "规划未来 - 个人金融规划",
                icon: "fas fa-road",
                objectives: [
                    "学会制定完整的财务规划",
                    "了解不同人生阶段的理财重点",
                    "掌握投资组合构建的方法",
                    "建立风险管理体系",
                    "树立正确的财富观念"
                ],
                content: `
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">人生财务规划的重要性</h3>
                    <p class="mb-4 leading-relaxed">
                        财务规划就是为实现人生目标而制定的资金安排计划。它不仅仅是投资理财，更是对整个人生的财务安排。一个好的财务规划能够帮助我们在不同的人生阶段都有充足的资金支持，最终实现财务自由和人生理想。
                    </p>

                    <div class="grid md:grid-cols-4 gap-3 mb-6">
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-seedling text-2xl text-green-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">青年期</h6>
                            <p class="text-xs text-gray-300">20-35岁</p>
                            <p class="text-xs text-gray-300">积累期</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-chart-line text-2xl text-blue-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">中年期</h6>
                            <p class="text-xs text-gray-300">35-50岁</p>
                            <p class="text-xs text-gray-300">成长期</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-mountain text-2xl text-yellow-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">成熟期</h6>
                            <p class="text-xs text-gray-300">50-65岁</p>
                            <p class="text-xs text-gray-300">稳健期</p>
                        </div>
                        <div class="glass p-4 rounded-lg text-center">
                            <i class="fas fa-umbrella text-2xl text-purple-400 mb-2"></i>
                            <h6 class="font-semibold mb-2">退休期</h6>
                            <p class="text-xs text-gray-300">65岁以后</p>
                            <p class="text-xs text-gray-300">保守期</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">投资组合的构建原则</h4>
                    <p class="mb-4 leading-relaxed">
                        投资组合构建要遵循分散化原则，根据个人的风险承受能力、投资期限和收益目标来配置不同的资产。一般来说，年轻人可以配置更多的股票类资产，年长者应该增加债券等稳健资产的比例。
                    </p>

                    <div class="bg-gradient-to-r from-purple-500/20 to-blue-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-purple-300"><i class="fas fa-pie-chart mr-2"></i>经典资产配置模型</h5>
                        <div class="grid md:grid-cols-3 gap-4">
                            <div>
                                <h6 class="font-medium mb-2 text-green-300">保守型（30岁）</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 股票基金：70%</li>
                                    <li>• 债券基金：20%</li>
                                    <li>• 货币基金：10%</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-blue-300">平衡型（45岁）</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 股票基金：55%</li>
                                    <li>• 债券基金：35%</li>
                                    <li>• 货币基金：10%</li>
                                </ul>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-yellow-300">稳健型（60岁）</h6>
                                <ul class="text-sm space-y-1 text-gray-300">
                                    <li>• 股票基金：40%</li>
                                    <li>• 债券基金：50%</li>
                                    <li>• 货币基金：10%</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mb-3 text-purple-400">财务自由的实现路径</h4>
                    <p class="mb-4 leading-relaxed">
                        财务自由不是一夜暴富，而是通过长期的规划和坚持实现的。它意味着被动收入能够覆盖生活支出，不再需要为了生存而工作。实现财务自由需要提高收入、控制支出、合理投资三管齐下。
                    </p>

                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-arrow-up text-2xl text-green-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">提高收入</h5>
                            <ul class="text-sm space-y-1 text-gray-300">
                                <li>• 提升职业技能</li>
                                <li>• 发展副业收入</li>
                                <li>• 创业投资</li>
                                <li>• 被动收入来源</li>
                            </ul>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-cut text-2xl text-blue-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">控制支出</h5>
                            <ul class="text-sm space-y-1 text-gray-300">
                                <li>• 制定预算计划</li>
                                <li>• 区分需要和想要</li>
                                <li>• 避免消费陷阱</li>
                                <li>• 延迟满足</li>
                            </ul>
                        </div>
                        <div class="glass p-4 rounded-lg">
                            <i class="fas fa-chart-line text-2xl text-purple-400 mb-2"></i>
                            <h5 class="font-semibold mb-2">合理投资</h5>
                            <ul class="text-sm space-y-1 text-gray-300">
                                <li>• 长期投资理念</li>
                                <li>• 分散投资风险</li>
                                <li>• 复利效应</li>
                                <li>• 定期调整</li>
                            </ul>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 p-6 rounded-lg mb-6">
                        <h5 class="font-semibold mb-4 text-yellow-300"><i class="fas fa-lightbulb mr-2"></i>金融入门课程总结</h5>
                        <p class="text-gray-300 mb-4">
                            通过这9节课的学习，我们从金融的基本概念开始，逐步了解了货币银行、利率通胀、投资工具、风险管理、个人理财、金融市场、金融科技，最后学习了完整的财务规划。这些知识为我们的理财之路奠定了坚实的基础。
                        </p>
                        <div class="grid md:grid-cols-3 gap-4">
                            <div>
                                <h6 class="font-medium mb-2 text-blue-300">基础知识</h6>
                                <p class="text-sm text-gray-400">金融概念、货币银行、利率通胀</p>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-green-300">投资实践</h6>
                                <p class="text-sm text-gray-400">投资工具、风险管理、市场分析</p>
                            </div>
                            <div>
                                <h6 class="font-medium mb-2 text-purple-300">规划应用</h6>
                                <p class="text-sm text-gray-400">个人理财、科技应用、财务规划</p>
                            </div>
                        </div>
                    </div>
                `,
                examples: [
                    {
                        title: "复利的神奇力量",
                        content: "假设小明25岁开始每月投资1000元，年化收益率8%，到65岁退休时将积累约175万元。而如果35岁才开始投资同样金额，到65岁只能积累约75万元。这个例子说明了时间在投资中的重要性，越早开始投资，复利效应越明显。"
                    },
                    {
                        title: "完整的家庭财务规划",
                        content: "陈先生一家制定了完整的财务规划：应急基金6个月支出、子女教育金通过基金定投准备、夫妻养老金通过股票基金积累、购买了充足的保险保障。通过系统的规划和执行，他们在40岁时就基本实现了财务安全。"
                    }
                ],
                summary: [
                    "财务规划是实现人生目标的重要工具，需要根据不同人生阶段调整",
                    "投资组合要根据个人情况进行合理配置，平衡风险和收益",
                    "财务自由需要提高收入、控制支出、合理投资三方面努力",
                    "时间是投资的朋友，越早开始理财越能享受复利效应",
                    "金融知识的学习是一个持续过程，要不断更新和实践"
                ],
                thinking: [
                    "制定一个适合你当前阶段的财务规划",
                    "计算一下如果你现在开始定投，30年后能积累多少财富？",
                    "回顾这9节课的内容，哪些知识对你最有启发？"
                ],
                nextPreview: "恭喜你完成了金融入门的全部课程！现在你已经具备了基本的金融知识，可以开始你的理财之路了。记住，理财是一个长期的过程，需要持续学习和实践。"
            }
        };

        // 加载课程内容
        function loadCourseContent() {
            const contentDiv = document.getElementById('courseContent');
            const lesson = courseData[currentLesson];

            if (!lesson) {
                contentDiv.innerHTML = '<p class="text-center text-gray-400">课程内容加载中...</p>';
                return;
            }

            contentDiv.innerHTML = `
                <div class="glass p-8 rounded-3xl mb-8 animate__animated animate__fadeInUp">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 rounded-full glass flex items-center justify-center mr-4 glow">
                            <i class="${lesson.icon} text-2xl text-blue-400"></i>
                        </div>
                        <div>
                            <h2 class="text-3xl font-bold text-white mb-2">第${currentLesson}节</h2>
                            <h3 class="text-xl text-blue-300">${lesson.title}</h3>
                        </div>
                    </div>

                    <!-- 学习目标 -->
                    <div class="mb-8">
                        <h4 class="text-xl font-semibold mb-4 text-purple-400">
                            <i class="fas fa-target mr-2"></i>学习目标
                        </h4>
                        <div class="grid md:grid-cols-2 gap-3">
                            ${lesson.objectives.map((obj, index) => `
                                <div class="flex items-start space-x-3">
                                    <span class="w-6 h-6 rounded-full bg-blue-500 text-white text-sm flex items-center justify-center mt-0.5 flex-shrink-0">${index + 1}</span>
                                    <span class="text-gray-300">${obj}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 核心内容 -->
                    <div class="mb-8">
                        <h4 class="text-xl font-semibold mb-4 text-purple-400">
                            <i class="fas fa-book-open mr-2"></i>核心内容
                        </h4>
                        <div class="prose prose-invert max-w-none">
                            ${lesson.content}
                        </div>
                    </div>

                    <!-- 实例说明 -->
                    <div class="mb-8">
                        <h4 class="text-xl font-semibold mb-4 text-purple-400">
                            <i class="fas fa-lightbulb mr-2"></i>实例说明
                        </h4>
                        <div class="space-y-4">
                            ${lesson.examples.map((example, index) => `
                                <div class="glass p-6 rounded-lg">
                                    <h5 class="font-semibold mb-3 text-yellow-300">
                                        <i class="fas fa-star mr-2"></i>案例${index + 1}：${example.title}
                                    </h5>
                                    <p class="text-gray-300 leading-relaxed">${example.content}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 重点总结 -->
                    <div class="mb-8">
                        <h4 class="text-xl font-semibold mb-4 text-purple-400">
                            <i class="fas fa-clipboard-check mr-2"></i>重点总结
                        </h4>
                        <div class="space-y-3">
                            ${lesson.summary.map((point, index) => `
                                <div class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-400 mt-1"></i>
                                    <span class="text-gray-300">${point}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 课后思考 -->
                    <div class="mb-8">
                        <h4 class="text-xl font-semibold mb-4 text-purple-400">
                            <i class="fas fa-brain mr-2"></i>课后思考
                        </h4>
                        <div class="space-y-3">
                            ${lesson.thinking.map((question, index) => `
                                <div class="glass p-4 rounded-lg">
                                    <span class="text-blue-300 font-medium">思考题${index + 1}：</span>
                                    <span class="text-gray-300">${question}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 下节预告 -->
                    <div class="bg-gradient-to-r from-purple-500/20 to-blue-500/20 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold mb-2 text-blue-300">
                            <i class="fas fa-arrow-right mr-2"></i>下节预告
                        </h4>
                        <p class="text-gray-300">${lesson.nextPreview}</p>
                    </div>
                </div>
            `;

            // 添加淡入动画
            contentDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    </script>
</body>
</html>
