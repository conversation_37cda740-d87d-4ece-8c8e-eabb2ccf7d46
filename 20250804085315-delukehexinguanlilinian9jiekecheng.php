<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>德鲁克核心管理理念9节课程 - 项老师AI工作室</title>
    <meta name="description" content="系统学习德鲁克管理思想，9节课深入理解现代管理学之父的核心理念">
    
    <!-- CDN资源 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script defer src="https://cdn.bootcdn.net/ajax/libs/alpinejs/3.12.3/cdn.min.js"></script>
    
    <style>
        /* AI风格样式 */
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 粒子背景 */
        .particles {
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px; height: 2px;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-10vh) translateX(50px); opacity: 0; }
        }

        /* 玻璃透明效果 */
        .glass {
            background: linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1), 0 0 20px rgba(59,130,246,0.3);
        }

        /* 发光动画 */
        .glow {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 0 20px rgba(59,130,246,0.4); }
            50% { box-shadow: 0 0 30px rgba(59,130,246,0.8); }
        }

        /* 呼吸动画 */
        .breathe {
            animation: breathe 3s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* 底部固定导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0; left: 0; right: 0;
            height: 70px;
            background: linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255,255,255,0.3);
            z-index: 1000;
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* 移动端导航 */
        .mobile-nav {
            position: fixed;
            top: 0; right: -300px;
            width: 300px; height: 100vh;
            background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(26,26,46,0.9) 100%);
            backdrop-filter: blur(10px);
            transition: right 0.3s ease;
            z-index: 2000;
            padding-top: 60px;
        }

        .mobile-nav.open {
            right: 0;
        }

        /* 课程内容样式 */
        .lesson-content {
            padding-bottom: 100px; /* 为底部导航留空间 */
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .desktop-only { display: none; }
        }

        @media (min-width: 769px) {
            .mobile-only { display: none; }
            .bottom-nav { display: none; }
        }
    </style>
</head>
<body class="text-white">
    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 移动端导航按钮 -->
    <button class="mobile-only fixed top-4 right-4 z-30 glass p-3 rounded-full glow" onclick="toggleMobileNav()">
        <i class="fas fa-bars text-white"></i>
    </button>

    <!-- 移动端侧边导航 -->
    <div class="mobile-nav" id="mobileNav">
        <div class="p-6">
            <div class="flex items-center justify-between mb-8">
                <h3 class="text-xl font-bold text-white">课程目录</h3>
                <button onclick="toggleMobileNav()" class="text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <nav class="space-y-4">
                <a href="#lesson1" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-user-graduate mr-2"></i>第1节：德鲁克其人其思想
                </a>
                <a href="#lesson2" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-cogs mr-2"></i>第2节：管理的本质与目的
                </a>
                <a href="#lesson3" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-check-circle mr-2"></i>第3节：有效性是可以学会的
                </a>
                <a href="#lesson4" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-bullseye mr-2"></i>第4节：目标管理与自我控制
                </a>
                <a href="#lesson5" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-brain mr-2"></i>第5节：知识工作者的管理
                </a>
                <a href="#lesson6" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-lightbulb mr-2"></i>第6节：创新与企业家精神
                </a>
                <a href="#lesson7" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-flag mr-2"></i>第7节：组织的使命与战略
                </a>
                <a href="#lesson8" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-heart mr-2"></i>第8节：社会责任与管理伦理
                </a>
                <a href="#lesson9" class="block p-3 rounded-lg glass hover:bg-blue-500/20 transition-colors">
                    <i class="fas fa-rocket mr-2"></i>第9节：德鲁克思想的现代应用
                </a>
            </nav>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-8 relative z-10 lesson-content">
        <!-- 课程标题 -->
        <div class="text-center mb-12">
            <div class="inline-block glass p-8 rounded-3xl glow breathe">
                <i class="fas fa-user-graduate text-6xl text-blue-400 mb-4"></i>
                <h1 class="text-4xl md:text-6xl font-thin mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    德鲁克核心管理理念
                </h1>
                <p class="text-xl text-gray-300 tracking-wider">9节零基础课程</p>
            </div>
        </div>

        <!-- 制作信息栏 -->
        <div class="glass p-4 rounded-xl mb-8 text-center">
            <div class="flex flex-wrap justify-center items-center gap-4 text-sm text-gray-300">
                <span>
                    <i class="fas fa-user mr-1"></i>
                    制作者：<a href="#" class="text-blue-400 hover:text-blue-300">项老师超级总裁助理001</a>
                </span>
                <span>
                    <i class="fas fa-clock mr-1"></i>
                    用时：<span id="duration">计算中...</span>
                </span>
                <span>
                    <i class="fas fa-calendar mr-1"></i>
                    时间：2025-08-04 08:53:15 ~ <span id="endTime">进行中</span>
                </span>
            </div>
        </div>

        <!-- 课程内容将在这里动态生成 -->
        <div id="courseContent">
            <!-- 课程内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 底部固定导航栏 -->
    <div class="bottom-nav mobile-only">
        <div class="flex items-center justify-around h-full px-4">
            <button id="prevBtn" class="flex flex-col items-center justify-center p-2 rounded-lg glass hover:bg-blue-500/20 transition-colors" onclick="previousLesson()">
                <i class="fas fa-chevron-left text-xl mb-1"></i>
                <span class="text-xs">上一节</span>
            </button>
            
            <button id="playBtn" class="flex flex-col items-center justify-center p-3 rounded-full glass glow hover:bg-blue-500/20 transition-colors" onclick="toggleAudio()">
                <i class="fas fa-play text-2xl mb-1" id="playIcon"></i>
                <span class="text-xs">播放</span>
            </button>
            
            <button id="nextBtn" class="flex flex-col items-center justify-center p-2 rounded-lg glass hover:bg-blue-500/20 transition-colors" onclick="nextLesson()">
                <i class="fas fa-chevron-right text-xl mb-1"></i>
                <span class="text-xs">下一节</span>
            </button>
        </div>
    </div>

    <script>
        // 全局变量
        let currentLesson = 1;
        let totalLessons = 9;
        let isPlaying = false;
        let currentAudio = null;
        let audioCache = {};

        // 初始化应用
        function initializeApp() {
            createParticles();
            loadCourseContent();
            updateNavigationState();
        }

        // 创建粒子背景
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 移动端导航切换
        function toggleMobileNav() {
            const nav = document.getElementById('mobileNav');
            nav.classList.toggle('open');
        }

        // 导航功能
        function previousLesson() {
            if (currentLesson > 1) {
                currentLesson--;
                loadCourseContent();
                updateNavigationState();
            }
        }

        function nextLesson() {
            if (currentLesson < totalLessons) {
                currentLesson++;
                loadCourseContent();
                updateNavigationState();
            }
        }

        function updateNavigationState() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            if (prevBtn) {
                prevBtn.style.opacity = currentLesson > 1 ? '1' : '0.5';
                prevBtn.disabled = currentLesson <= 1;
            }
            
            if (nextBtn) {
                nextBtn.style.opacity = currentLesson < totalLessons ? '1' : '0.5';
                nextBtn.disabled = currentLesson >= totalLessons;
            }
        }

        // 音频播放功能
        function toggleAudio() {
            const playIcon = document.getElementById('playIcon');
            
            if (isPlaying) {
                if (currentAudio) {
                    currentAudio.pause();
                }
                playIcon.className = 'fas fa-play text-2xl mb-1';
                isPlaying = false;
            } else {
                playCurrentLessonAudio();
                playIcon.className = 'fas fa-pause text-2xl mb-1';
                isPlaying = true;
            }
        }

        function playCurrentLessonAudio() {
            // 这里将实现语音播放功能
            console.log('播放第' + currentLesson + '节课程音频');
        }

        // 加载课程内容（将在后续步骤中实现）
        function loadCourseContent() {
            // 课程内容将在后续步骤中动态生成
            console.log('加载第' + currentLesson + '节课程内容');
        }

        // 页面加载完成后启动
        document.addEventListener('DOMContentLoaded', () => {
            initializeApp();
        });
    </script>
</body>
</html>
