Add-Type -AssemblyName System.Net.Mail

$smtpServer = "smtp.qq.com"
$smtpPort = 465
$username = "<EMAIL>"
$password = "nsczwndkbuljbihj"
$from = "<EMAIL>"
$to = "<EMAIL>"
$subject = "金融入门9节课程已完成 - 用时13分36秒"

$body = @"
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>金融入门9节课程已完成</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center; color: white;">
      <h1>金融入门9节课程</h1>
      <p>完成通知</p>
    </div>
    <div style="padding: 20px; background: #f8f9fa;">
      <h3>尊敬的项老师，您好！</h3>
      <p>您指示的任务已经完成：</p>
      <ul>
        <li><strong>任务名称：</strong>金融入门9节课程</li>
        <li><strong>完成时间：</strong>2025-08-04 07:59:47～2025-08-04 08:13:23</li>
        <li><strong>用时：</strong>13分36秒</li>
        <li><strong>课程内容：</strong>9节完整课程，每节300+字详细内容</li>
        <li><strong>特色功能：</strong>AI风格界面、Loading动画、语音朗读、移动端优化</li>
      </ul>
      <div style="text-align: center; margin: 20px 0;">
        <p><strong>本地文件：</strong>20250804075947-jinrongrumen9jiekecheng.php</p>
        <p><strong>文件大小：</strong>约200KB</p>
      </div>
      <h4>课程大纲：</h4>
      <ol>
        <li>金融世界的大门 - 什么是金融？</li>
        <li>货币的故事 - 银行与货币体系</li>
        <li>数字背后的秘密 - 利率与通胀</li>
        <li>投资的第一步 - 股票、债券、基金入门</li>
        <li>投资的智慧 - 风险与收益的平衡</li>
        <li>财富管理 - 个人理财规划</li>
        <li>市场的脉搏 - 金融市场简介</li>
        <li>科技改变金融 - 金融科技的应用</li>
        <li>规划未来 - 个人金融规划</li>
      </ol>
      <p>此致<br>敬礼！</p>
      <p><strong>项老师超级总裁助理001</strong><br>2025-08-04 08:13:23</p>
    </div>
  </div>
</body>
</html>
"@

try {
    $smtp = New-Object System.Net.Mail.SmtpClient($smtpServer, $smtpPort)
    $smtp.EnableSsl = $true
    $smtp.Credentials = New-Object System.Net.NetworkCredential($username, $password)
    
    $message = New-Object System.Net.Mail.MailMessage($from, $to, $subject, $body)
    $message.IsBodyHtml = $true
    
    $smtp.Send($message)
    Write-Host "邮件发送成功！"
    Write-Host "收件人: $to"
    Write-Host "主题: $subject"
} catch {
    Write-Host "邮件发送失败: $($_.Exception.Message)"
}
